---
# Dotfiles Configuration Setup Playbook
#
# This playbook sets up a dotfiles configuration repository using a git bare repository.
# It performs the following actions:
# 1. Checks for SSH key existence and displays it for GitHub setup
# 2. Clones the cfg repository as a bare repository
# 3. Sets up the cfg function for managing dotfiles
# 4. Backs up existing dotfiles that would conflict
# 5. Checks out the configuration
# 6. Configures git to not show untracked files
#
# Prerequisites:
# - SSH key id_personal_ed25519 must exist in ~/.ssh/
# - The key must be added to GitHub before running this playbook
# - Target user must have access to the cfg repository
#
# Usage:
#   ansible-playbook -i hosts.yaml setup_dotfiles_cfg.yaml -e "hosts=linux_clients target_user=krizzo" -u aule -b
#
# Variables:
#   target_user: The user for whom to set up dotfiles (default: krizzo)
#   cfg_repo_url: The SSH URL of the cfg repository (default: **************:krizzo/cfg.git)
#   ssh_key_name: Name of the SSH key file (default: id_personal_ed25519)

- name: Setup Dotfiles Configuration Repository
  hosts: "{{ hosts | default('linux_clients') }}"
  gather_facts: true
  become: true
  remote_user: aule

  vars:
    # Default values - can be overridden with -e parameters
    _target_user: "{{ target_user | default('krizzo') }}"
    _cfg_repo_url: "{{ cfg_repo_url | default('**************:krizzo/cfg.git') }}"
    _ssh_key_name: "{{ ssh_key_name | default('id_personal_ed25519') }}"

    # Derived paths using the resolved variables
    ssh_key_path: "/home/<USER>/.ssh/{{ _ssh_key_name }}"
    ssh_pub_key_path: "/home/<USER>/.ssh/{{ _ssh_key_name }}.pub"
    cfg_dir: "/home/<USER>/.cfg"
    backup_dir: "/home/<USER>/.config-backup"

  tasks:
    - name: Display playbook information
      ansible.builtin.debug:
        msg: |
          Setting up dotfiles configuration for user: {{ _target_user }}
          Repository: {{ _cfg_repo_url }}
          SSH Key: {{ ssh_key_path }}
          Target Host: {{ inventory_hostname }}
      tags: ['info']

    # Phase 1: Prerequisites Check
    - name: Phase 1 - Check if target user exists
      ansible.builtin.getent:
        database: passwd
        key: "{{ _target_user }}"
      register: user_check
      failed_when: false
      tags: ['validation']

    - name: Phase 1 - Fail if target user doesn't exist
      ansible.builtin.fail:
        msg: "Target user '{{ _target_user }}' does not exist on {{ inventory_hostname }}"
      when: user_check.ansible_facts.getent_passwd[_target_user] is not defined
      tags: ['validation']

    - name: Phase 1 - Check if SSH private key exists
      ansible.builtin.stat:
        path: "{{ ssh_key_path }}"
      register: ssh_private_key
      tags: ['validation', 'ssh']

    - name: Phase 1 - Check if SSH public key exists
      ansible.builtin.stat:
        path: "{{ ssh_pub_key_path }}"
      register: ssh_public_key
      tags: ['validation', 'ssh']

    - name: Phase 1 - Fail if SSH keys don't exist
      ansible.builtin.fail:
        msg: |
          SSH keys not found for user {{ _target_user }}:
          Private key: {{ ssh_key_path }} (exists: {{ ssh_private_key.stat.exists }})
          Public key: {{ ssh_pub_key_path }} (exists: {{ ssh_public_key.stat.exists }})

          Please generate SSH keys first:
          ssh-keygen -t ed25519 -f ~/.ssh/{{ _ssh_key_name }} -C "{{ _target_user }}@{{ inventory_hostname }}"
      when: not ssh_private_key.stat.exists or not ssh_public_key.stat.exists
      tags: ['validation', 'ssh']

    # Phase 2: Display SSH Public Key
    - name: Phase 2 - Read SSH public key content
      ansible.builtin.slurp:
        src: "{{ ssh_pub_key_path }}"
      register: ssh_pub_key_content
      become_user: "{{ _target_user }}"
      tags: ['ssh', 'github']

    - name: Phase 2 - Display SSH public key for GitHub setup
      ansible.builtin.pause:
        prompt: |

          ==========================================
          IMPORTANT: GitHub SSH Key Setup Required
          ==========================================

          Before continuing, you MUST add the following SSH public key to your GitHub account:

          {{ ssh_pub_key_content.content | b64decode | trim }}

          Steps to add the key to GitHub:
          1. Go to https://github.com/settings/keys
          2. Click "New SSH key"
          3. Give it a title (e.g., "{{ _target_user }}@{{ inventory_hostname }}")
          4. Paste the above key into the "Key" field
          5. Click "Add SSH key"

          Press ENTER to continue once you have added the key to GitHub...
      tags: ['ssh', 'github']

    # Phase 3: Git Configuration Setup
    - name: Phase 3 - Check if .cfg directory already exists
      ansible.builtin.stat:
        path: "{{ cfg_dir }}"
      register: cfg_dir_stat
      become_user: "{{ _target_user }}"
      tags: ['git', 'setup']

    - name: Phase 3 - Exit successfully if .cfg directory already exists
      ansible.builtin.debug:
        msg: |
          ==========================================
          Dotfiles Configuration Already Setup!
          ==========================================

          The .cfg directory already exists at: {{ cfg_dir }}
          This indicates that dotfiles configuration is already set up for user {{ _target_user }}.

          If you need to re-setup the configuration, please manually remove the .cfg directory first:
          rm -rf {{ cfg_dir }}

          Current cfg function status can be checked with:
          - For bash: grep -q "function cfg" ~/.bashrc && echo "cfg function exists" || echo "cfg function missing"
          - For zsh: grep -q "function cfg" ~/.zshrc && echo "cfg function exists" || echo "cfg function missing"

          Exiting playbook successfully.
      when: cfg_dir_stat.stat.exists
      tags: ['git', 'setup', 'exit']

    - name: Phase 3 - End play if .cfg directory already exists
      ansible.builtin.meta: end_host
      when: cfg_dir_stat.stat.exists
      tags: ['git', 'setup', 'exit']

    - name: Phase 3 - Clone cfg repository as bare repository
      ansible.builtin.git:
        repo: "{{ _cfg_repo_url }}"
        dest: "{{ cfg_dir }}"
        bare: true
        key_file: "{{ ssh_key_path }}"
        accept_hostkey: true
      become_user: "{{ _target_user }}"
      environment:
        GIT_SSH_COMMAND: "ssh -i {{ ssh_key_path }} -o StrictHostKeyChecking=no"
      tags: ['git', 'clone']

    # Phase 4: Backup and Checkout
    - name: Phase 4 - Create backup directory
      ansible.builtin.file:
        path: "{{ backup_dir }}"
        state: directory
        mode: '0755'
        owner: "{{ _target_user }}"
        group: "{{ _target_user }}"
      become_user: "{{ _target_user }}"
      tags: ['backup']

    - name: Phase 4 - Attempt initial checkout to identify conflicts
      ansible.builtin.shell: |
        /usr/bin/git --git-dir={{ cfg_dir }}/ --work-tree=/home/<USER>
      register: initial_checkout
      become_user: "{{ _target_user }}"
      changed_when: false
      tags: ['git', 'checkout']

    - name: Phase 4 - Backup conflicting files
      ansible.builtin.shell: |
        /usr/bin/git --git-dir={{ cfg_dir }}/ --work-tree=/home/<USER>
        egrep "\s+\." | awk '{print $1}' | \
        xargs -I{} sh -c 'if [ -f "/home/<USER>/{}" ]; then mv "/home/<USER>/{}" "{{ backup_dir }}/{}"; fi'
      become_user: "{{ _target_user }}"
      when: "'error:' in initial_checkout.stdout or 'would be overwritten' in initial_checkout.stdout"
      tags: ['backup', 'git']

    - name: Phase 4 - Perform final checkout
      ansible.builtin.shell: |
        /usr/bin/git --git-dir={{ cfg_dir }}/ --work-tree=/home/<USER>
      become_user: "{{ _target_user }}"
      register: final_checkout
      tags: ['git', 'checkout']

    - name: Phase 4 - Configure git to not show untracked files
      ansible.builtin.shell: |
        /usr/bin/git --git-dir={{ cfg_dir }}/ --work-tree=/home/<USER>
      become_user: "{{ _target_user }}"
      tags: ['git', 'config']

    # Phase 5: Setup cfg function
    - name: Phase 5 - Check if cfg function exists in .bashrc
      ansible.builtin.shell: |
        grep -q "function cfg" "/home/<USER>/.bashrc" 2>/dev/null || echo "not_found"
      register: bashrc_cfg_check
      become_user: "{{ _target_user }}"
      changed_when: false
      failed_when: false
      when: ansible_facts.getent_passwd[_target_user][5] == '/bin/bash'
      tags: ['shell', 'function', 'check']

    - name: Phase 5 - Add cfg function to .bashrc
      ansible.builtin.blockinfile:
        path: "/home/<USER>/.bashrc"
        block: |
          # cfg function for managing dotfiles
          function cfg {
             /usr/bin/git --git-dir=$HOME/.cfg/ --work-tree=$HOME $@
          }
        marker: "# {mark} ANSIBLE MANAGED BLOCK - cfg function"
        create: true
        owner: "{{ _target_user }}"
        group: "{{ _target_user }}"
        mode: '0644'
      become_user: "{{ _target_user }}"
      when:
        - ansible_facts.getent_passwd[_target_user][5] == '/bin/bash'
        - "'not_found' in bashrc_cfg_check.stdout"
      tags: ['shell', 'function']

    - name: Phase 5 - Check if cfg function exists in .zshrc
      ansible.builtin.shell: |
        grep -q "function cfg" "/home/<USER>/.zshrc" 2>/dev/null || echo "not_found"
      register: zshrc_cfg_check
      become_user: "{{ _target_user }}"
      changed_when: false
      failed_when: false
      when: ansible_facts.getent_passwd[_target_user][5] == '/usr/bin/zsh'
      tags: ['shell', 'function', 'check']

    - name: Phase 5 - Add cfg function to .zshrc (if zsh is the shell)
      ansible.builtin.blockinfile:
        path: "/home/<USER>/.zshrc"
        block: |
          # cfg function for managing dotfiles
          function cfg {
             /usr/bin/git --git-dir=$HOME/.cfg/ --work-tree=$HOME $@
          }
        marker: "# {mark} ANSIBLE MANAGED BLOCK - cfg function"
        create: true
        owner: "{{ _target_user }}"
        group: "{{ _target_user }}"
        mode: '0644'
      become_user: "{{ _target_user }}"
      when:
        - ansible_facts.getent_passwd[_target_user][5] == '/usr/bin/zsh'
        - "'not_found' in zshrc_cfg_check.stdout"
      tags: ['shell', 'function']

    # Phase 6: Verification and Completion
    - name: Phase 6 - Verify cfg repository status
      ansible.builtin.shell: |
        /usr/bin/git --git-dir={{ cfg_dir }}/ --work-tree=/home/<USER>
      become_user: "{{ _target_user }}"
      register: cfg_status
      changed_when: false
      tags: ['verification']

    - name: Phase 6 - Display setup completion
      ansible.builtin.debug:
        msg: |
          ==========================================
          Dotfiles Configuration Setup Complete!
          ==========================================

          Setup Summary:
          - User: {{ _target_user }}
          - Repository: {{ _cfg_repo_url }}
          - Configuration directory: {{ cfg_dir }}
          - Backup directory: {{ backup_dir }}

          Shell Function Status:
          {% if ansible_facts.getent_passwd[_target_user][5] == '/bin/bash' %}
          - .bashrc cfg function: {{ 'Already exists' if 'not_found' not in bashrc_cfg_check.stdout else 'Added' }}
          {% endif %}
          {% if ansible_facts.getent_passwd[_target_user][5] == '/usr/bin/zsh' %}
          - .zshrc cfg function: {{ 'Already exists' if 'not_found' not in zshrc_cfg_check.stdout else 'Added' }}
          {% endif %}

          Usage Examples:
          - Check status: cfg status
          - Add files: cfg add .vimrc
          - Commit changes: cfg commit -m "Update vimrc"
          - Push changes: cfg push
          - Pull updates: cfg pull

          To start using the cfg function immediately, run:
          source ~/.bashrc  (for bash)
          {% if ansible_facts.getent_passwd[_target_user][5] == '/usr/bin/zsh' %}
          source ~/.zshrc   (for zsh)
          {% endif %}

          {% if cfg_status.stdout %}
          Note: There are uncommitted changes in your dotfiles repository.
          Run 'cfg status' to see what files have been modified.
          {% endif %}
      tags: ['completion']
